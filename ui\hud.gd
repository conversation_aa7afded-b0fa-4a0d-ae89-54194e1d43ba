class_name HUD
extends <PERSON><PERSON><PERSON><PERSON><PERSON>

@onready var health_bar := %HealthBar as TextureProgressBar
@onready var money_label := %MoneyLabel as Label
@onready var wave_label := %WaveLabel as Label
@onready var next_wave_panel := %NextWave as Panel
@onready var countdown_label := %Countdown as Label
@onready var next_wave_timer := %Timer as Timer
@onready var placement_button := %PlacementButton as Button
@onready var instructions_label := %InstructionsLabel as Label

var tower_slot_placer: TowerSlotPlacer

func initialize(max_health: int) -> void:
	health_bar.max_value = max_health
	health_bar.value = health_bar.max_value

	# Connect to tower slot placer
	var map = get_node("/root/Map")
	if map:
		tower_slot_placer = map.get_node("TowerSlotPlacer")
		if tower_slot_placer:
			tower_slot_placer.placement_mode_changed.connect(_on_placement_mode_changed)
			tower_slot_placer.tower_slot_placed.connect(_on_tower_slot_placed)

	# Initialize placement button text
	_update_placement_button_text()


func _on_objective_health_changed(health: int) -> void:
	health_bar.value = health


func _on_spawner_countdown_started(seconds: float) -> void:
	next_wave_panel.show()
	next_wave_timer.start(seconds)


func _on_spawner_wave_started(current_wave: int) -> void:
	wave_label.text = "Wave: %d" % current_wave


func _on_money_changed(money: int) -> void:
	money_label.text = str(money)
	# Update placement button text when money changes
	if not placement_button.text.begins_with("Exit"):
		_update_placement_button_text()


func _process(_delta: float) -> void:
	if not next_wave_timer.is_stopped():
		countdown_label.text = str(ceil(next_wave_timer.time_left))


func _on_timer_timeout():
	next_wave_panel.hide()


func _on_placement_button_pressed():
	if tower_slot_placer:
		tower_slot_placer.toggle_placement_mode()


func _on_placement_mode_changed(enabled: bool):
	if enabled:
		placement_button.text = "Exit Placement"
		placement_button.modulate = Color.YELLOW
		instructions_label.text = "PLACEMENT MODE ACTIVE\nLeft click: Place slot | Right click: Cancel\nESC: Exit placement mode\nGreen = Valid | Red = Invalid | Orange = No money"
	else:
		_update_placement_button_text()
		placement_button.modulate = Color.WHITE
		instructions_label.text = "Press T or click button to place tower slots\nLeft click: Place slot | Right click: Cancel\nESC: Exit placement mode\nYou have 20 minutes to prepare!"

func _update_placement_button_text():
	if tower_slot_placer:
		var cost = tower_slot_placer.get_current_tower_slot_cost()
		placement_button.text = "Place Tower Slot ($" + str(cost) + ")"
	else:
		placement_button.text = "Place Tower Slots"

func _on_tower_slot_placed(cost: int):
	# Update button text when a tower slot is placed
	_update_placement_button_text()

	# Update instructions to show progress
	if tower_slot_placer and not placement_button.text.begins_with("Exit"):
		var slots_placed = tower_slot_placer.tower_slots_placed
		instructions_label.text = "Press T or click button to place tower slots\nTower slots placed: " + str(slots_placed) + "\nYou have 20 minutes to prepare!"
