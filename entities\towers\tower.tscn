[gd_scene load_steps=8 format=3 uid="uid://babinwqpjwhmn"]

[ext_resource type="Script" uid="uid://d2vhit5mjwsm5" path="res://entities/towers/tower.gd" id="1_u2rls"]
[ext_resource type="Texture2D" uid="uid://nrm8gauiksh3" path="res://assets/towers/gatling/tier1/base_idle_00.png" id="1_ufm6q"]
[ext_resource type="PackedScene" uid="uid://bswr4r1sn2dki" path="res://entities/shooter.tscn" id="3_cw0b1"]
[ext_resource type="AudioStream" uid="uid://dkjcbquvlvltr" path="res://assets/sounds/gatling.wav" id="4_ppwhr"]
[ext_resource type="PackedScene" uid="uid://tyfxjrygr03c" path="res://ui/entity_hud.tscn" id="4_psd3c"]
[ext_resource type="PackedScene" uid="uid://crppyn467jwhu" path="res://entities/explosion_vfx/explosion.tscn" id="5_14fh1"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_xawuj"]
size = Vector2(119, 114)

[node name="Tower" type="StaticBody2D"]
collision_layer = 2
collision_mask = 33
input_pickable = true
script = ExtResource("1_u2rls")

[node name="Foundation" type="Sprite2D" parent="."]
texture = ExtResource("1_ufm6q")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(0.5, 5)
shape = SubResource("RectangleShape2D_xawuj")

[node name="Shooter" parent="." instance=ExtResource("3_cw0b1")]
rot_speed = 10.0
projectile_damage = 5

[node name="MuzzleFlash" parent="Shooter" index="1"]
autoplay = "idle"

[node name="ShootSound" parent="Shooter" index="2"]
stream = ExtResource("4_ppwhr")

[node name="Explosion" parent="." instance=ExtResource("5_14fh1")]

[node name="UI" type="Node2D" parent="."]
z_index = 50

[node name="EntityHUD" parent="UI" instance=ExtResource("4_psd3c")]
offset_left = -57.0
offset_top = -92.0
offset_right = -57.0
offset_bottom = -92.0
scale = Vector2(1.2, 1)

[connection signal="mouse_entered" from="." to="." method="_on_mouse_entered"]
[connection signal="mouse_exited" from="." to="." method="_on_mouse_exited"]
[connection signal="has_shot" from="Shooter" to="." method="_on_shooter_has_shot"]
[connection signal="animation_finished" from="Shooter/Gun" to="." method="_on_gun_animation_finished"]

[editable path="Shooter"]
