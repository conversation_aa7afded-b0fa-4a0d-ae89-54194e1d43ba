[gd_scene load_steps=16 format=3 uid="uid://carh6bbm1af4b"]

[ext_resource type="Script" uid="uid://br35it6till2l" path="res://ui/hud.gd" id="1_1i8l6"]
[ext_resource type="Texture2D" uid="uid://ix8s1c31gft5" path="res://assets/ui/heads_up/headsup_panel.png" id="1_gn7ca"]
[ext_resource type="Texture2D" uid="uid://b84ro4mvacxb5" path="res://assets/ui/heads_up/tower_icon.png" id="2_u6cu1"]
[ext_resource type="Texture2D" uid="uid://c61neo8s8gdpu" path="res://assets/ui/heads_up/lifebar_window.png" id="3_ukyhd"]
[ext_resource type="Texture2D" uid="uid://bqx30c34ubc70" path="res://assets/ui/heads_up/lifebar.png" id="4_enk3r"]
[ext_resource type="Texture2D" uid="uid://cwpm0bncljojn" path="res://assets/ui/heads_up/coin_icon.png" id="5_af6wl"]
[ext_resource type="Texture2D" uid="uid://2bx2lgilbqsm" path="res://assets/ui/heads_up/currency_window.png" id="6_mgoin"]
[ext_resource type="PackedScene" uid="uid://wp2xxnx6uw1u" path="res://ui/pause.tscn" id="8_qxaai"]
[ext_resource type="PackedScene" uid="uid://dkovbmmfciv1f" path="res://ui/game_over.tscn" id="9_lnowy"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_g2s2v"]
texture = ExtResource("1_gn7ca")

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_v21pb"]
texture = ExtResource("6_mgoin")

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_mwlci"]
texture = ExtResource("6_mgoin")
modulate_color = Color(1, 1, 1, 0.627451)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_3fwpv"]
texture = ExtResource("6_mgoin")
modulate_color = Color(1, 1, 1, 0.627451)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_weq2x"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_nltwi"]

[node name="HUD" type="CanvasLayer"]
layer = 5
script = ExtResource("1_1i8l6")

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="Panel" type="Panel" parent="Control"]
layout_mode = 0
offset_left = 1566.0
offset_top = 12.0
offset_right = 1907.0
offset_bottom = 160.0
theme_override_styles/panel = SubResource("StyleBoxTexture_g2s2v")

[node name="Rows" type="VBoxContainer" parent="Control/Panel"]
layout_mode = 0
offset_left = 15.0
offset_top = 40.0
offset_right = 55.0
offset_bottom = 80.0

[node name="Health" type="HBoxContainer" parent="Control/Panel/Rows"]
layout_mode = 2

[node name="Icon" type="TextureRect" parent="Control/Panel/Rows/Health"]
layout_mode = 2
texture = ExtResource("2_u6cu1")

[node name="HealthBar" type="TextureProgressBar" parent="Control/Panel/Rows/Health"]
unique_name_in_owner = true
layout_mode = 2
value = 100.0
texture_under = ExtResource("3_ukyhd")
texture_progress = ExtResource("4_enk3r")
texture_progress_offset = Vector2(4, 4)

[node name="Money" type="HBoxContainer" parent="Control/Panel/Rows"]
layout_mode = 2

[node name="Icon" type="TextureRect" parent="Control/Panel/Rows/Money"]
layout_mode = 2
texture = ExtResource("5_af6wl")

[node name="MoneyLabel" type="Label" parent="Control/Panel/Rows/Money"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
theme_override_font_sizes/font_size = 30
theme_override_styles/normal = SubResource("StyleBoxTexture_v21pb")
text = "10000"
horizontal_alignment = 2
vertical_alignment = 1

[node name="WaveLabel" type="Label" parent="Control"]
unique_name_in_owner = true
layout_mode = 0
offset_left = 11.0
offset_top = 8.0
offset_right = 155.0
offset_bottom = 72.0
theme_override_font_sizes/font_size = 24
theme_override_styles/normal = SubResource("StyleBoxTexture_mwlci")
text = "Wave: 1
"
horizontal_alignment = 1
vertical_alignment = 1

[node name="PlacementButton" type="Button" parent="Control"]
unique_name_in_owner = true
layout_mode = 0
offset_left = 11.0
offset_top = 80.0
offset_right = 200.0
offset_bottom = 120.0
theme_override_font_sizes/font_size = 18
text = "Place Tower Slots"

[node name="InstructionsLabel" type="Label" parent="Control"]
unique_name_in_owner = true
layout_mode = 0
offset_left = 11.0
offset_top = 130.0
offset_right = 400.0
offset_bottom = 200.0
theme_override_font_sizes/font_size = 14
text = "SURVIVAL PROTOCOL ACTIVE
Press T or click button to deploy defense slots
Left click: Deploy | Right click: Abort
ESC: Exit deployment
You have 20 minutes to prepare for the assault!"
autowrap_mode = 2

[node name="NextWave" type="Panel" parent="Control"]
unique_name_in_owner = true
layout_mode = 0
offset_left = 788.0
offset_top = 10.0
offset_right = 1132.0
offset_bottom = 74.0
theme_override_styles/panel = SubResource("StyleBoxTexture_3fwpv")

[node name="Rows" type="HBoxContainer" parent="Control/NextWave"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -157.5
offset_top = -24.5
offset_right = 157.5
offset_bottom = 24.5
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 20

[node name="Title" type="Label" parent="Control/NextWave/Rows"]
layout_mode = 2
theme_override_font_sizes/font_size = 36
theme_override_styles/normal = SubResource("StyleBoxEmpty_weq2x")
text = "Next wave in:"
horizontal_alignment = 1

[node name="Countdown" type="Label" parent="Control/NextWave/Rows"]
unique_name_in_owner = true
layout_mode = 2
theme_override_font_sizes/font_size = 48
theme_override_styles/normal = SubResource("StyleBoxEmpty_nltwi")
text = "5"

[node name="Timer" type="Timer" parent="Control/NextWave"]
unique_name_in_owner = true
one_shot = true

[node name="Menus" type="Control" parent="."]
layout_mode = 3
anchors_preset = 0
offset_right = 40.0
offset_bottom = 40.0

[node name="Pause" parent="Menus" instance=ExtResource("8_qxaai")]
visible = false
layout_mode = 0
anchors_preset = 0
anchor_left = 0.0
anchor_top = 0.0
anchor_right = 0.0
anchor_bottom = 0.0
offset_left = 0.0
offset_top = 0.0
offset_right = 1000.0
offset_bottom = 500.005
grow_horizontal = 1
grow_vertical = 1

[node name="GameOver" parent="Menus" instance=ExtResource("9_lnowy")]
visible = false
layout_mode = 0
anchors_preset = 0
anchor_left = 0.0
anchor_top = 0.0
anchor_right = 0.0
anchor_bottom = 0.0
offset_left = 516.0
offset_top = 294.0
offset_right = 1516.0
offset_bottom = 794.004
grow_horizontal = 1
grow_vertical = 1

[connection signal="timeout" from="Control/NextWave/Timer" to="." method="_on_timer_timeout"]
[connection signal="pressed" from="Control/PlacementButton" to="." method="_on_placement_button_pressed"]
