extends Node2D
class_name TowerSlotPlacer

signal placement_mode_changed(enabled: bool)

@export var tower_slot_scene: PackedScene = preload("res://entities/towers/tower_slot.tscn")
@export var placement_indicator_color := Color.GREEN
@export var invalid_placement_color := Color.RED
@export var indicator_radius := 60.0

var placement_mode := false
var placement_indicator_position := Vector2.ZERO
var is_valid_placement := false

@onready var map := get_parent() as Node2D
@onready var tilemap := map.get_node("TileMap") as TileMap

func _ready():
	set_process_unhandled_input(false)

func _draw():
	if placement_mode:
		var color = placement_indicator_color if is_valid_placement else invalid_placement_color
		draw_circle(to_local(placement_indicator_position), indicator_radius, Color(color.r, color.g, color.b, 0.3))
		draw_arc(to_local(placement_indicator_position), indicator_radius, 0, TAU, 32, color, 3.0)

func _unhandled_input(event: InputEvent):
	if event is InputEventKey and event.pressed:
		if event.keycode == KEY_T:  # Toggle placement mode with 'T' key
			toggle_placement_mode()
			return

	if not placement_mode:
		return

	if event is InputEventMouseMotion:
		placement_indicator_position = get_global_mouse_position()
		is_valid_placement = _is_valid_placement_position(placement_indicator_position)
		queue_redraw()

	elif event is InputEventMouseButton:
		if event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
			if is_valid_placement:
				_place_tower_slot(placement_indicator_position)
		elif event.pressed and event.button_index == MOUSE_BUTTON_RIGHT:
			toggle_placement_mode()
		elif event.pressed and event.button_index == MOUSE_BUTTON_MIDDLE:
			# Middle click to cancel placement mode
			toggle_placement_mode()

	elif event is InputEventKey and event.pressed:
		if event.keycode == KEY_ESCAPE:
			# Escape key to cancel placement mode
			if placement_mode:
				toggle_placement_mode()

func toggle_placement_mode():
	placement_mode = !placement_mode
	set_process_unhandled_input(placement_mode)
	
	if placement_mode:
		placement_indicator_position = get_global_mouse_position()
		is_valid_placement = _is_valid_placement_position(placement_indicator_position)
	
	queue_redraw()
	placement_mode_changed.emit(placement_mode)

func _is_valid_placement_position(pos: Vector2) -> bool:
	# Check if position is on a navigation path (where enemies walk)
	if _is_on_navigation_path(pos):
		return false
	
	# Check if position overlaps with existing tower slots
	if _overlaps_with_tower_slot(pos):
		return false
	
	# Check if position overlaps with existing towers
	if _overlaps_with_tower(pos):
		return false
	
	# Check if position is within map bounds
	if not _is_within_map_bounds(pos):
		return false
	
	return true

func _is_on_navigation_path(pos: Vector2) -> bool:
	# Get the tile position
	var tile_pos = tilemap.local_to_map(tilemap.to_local(pos))

	# Check if this tile has navigation data (indicating it's a path)
	var tile_data = tilemap.get_cell_tile_data(0, tile_pos)
	if tile_data and tile_data.get_navigation_polygon(0):
		return true

	# Also check if position is within a certain distance of navigation mesh
	var space_state = get_world_2d().direct_space_state
	var query = PhysicsPointQueryParameters2D.new()
	query.position = pos
	query.collision_mask = 1  # Check against navigation layer

	# Use a small radius to check nearby navigation areas
	var nearby_check_radius = 64.0  # Half a tile size
	for offset in [Vector2.ZERO, Vector2(nearby_check_radius, 0), Vector2(-nearby_check_radius, 0),
				   Vector2(0, nearby_check_radius), Vector2(0, -nearby_check_radius)]:
		var check_pos = pos + offset
		var check_tile_pos = tilemap.local_to_map(tilemap.to_local(check_pos))
		var check_tile_data = tilemap.get_cell_tile_data(0, check_tile_pos)
		if check_tile_data and check_tile_data.get_navigation_polygon(0):
			return true

	return false

func _overlaps_with_tower_slot(pos: Vector2) -> bool:
	# Check collision with existing tower slots using physics queries
	var space_state = get_world_2d().direct_space_state
	var query = PhysicsShapeQueryParameters2D.new()

	# Create a circle shape for the tower slot area
	var shape = CircleShape2D.new()
	shape.radius = indicator_radius
	query.shape = shape
	query.transform.origin = pos
	query.collision_mask = 8  # tower_slot layer

	var result = space_state.intersect_shape(query)
	return result.size() > 0

func _overlaps_with_tower(pos: Vector2) -> bool:
	# Check collision with existing towers
	var space_state = get_world_2d().direct_space_state
	var query = PhysicsShapeQueryParameters2D.new()

	# Create a circle shape for the tower area
	var shape = CircleShape2D.new()
	shape.radius = indicator_radius
	query.shape = shape
	query.transform.origin = pos
	query.collision_mask = 2  # tower layer

	var result = space_state.intersect_shape(query)
	return result.size() > 0

func _is_within_map_bounds(pos: Vector2) -> bool:
	var map_rect = tilemap.get_used_rect()
	var tile_size = tilemap.tile_set.tile_size
	
	var map_bounds = Rect2(
		map_rect.position * tile_size,
		map_rect.size * tile_size
	)
	
	return map_bounds.has_point(tilemap.to_local(pos))

func _place_tower_slot(pos: Vector2):
	var tower_slot = tower_slot_scene.instantiate()
	map.add_child(tower_slot)
	tower_slot.global_position = pos

	# Add some visual feedback for successful placement
	print("Tower slot placed at: ", pos)

	# Create a brief visual effect for successful placement
	_create_placement_effect(pos)

	# Update the indicator position and validity for the next placement
	is_valid_placement = _is_valid_placement_position(placement_indicator_position)
	queue_redraw()

func _create_placement_effect(pos: Vector2):
	# Create a simple tween effect to show successful placement
	var tween = create_tween()
	var original_color = placement_indicator_color
	placement_indicator_color = Color.WHITE
	queue_redraw()

	tween.tween_callback(func():
		placement_indicator_color = original_color
		queue_redraw()
	).set_delay(0.1)
