extends Node2D

@export var starting_money := 5000

@onready var tilemap := $TileMap as TileMap
@onready var camera := $Camera2D as Camera2D
@onready var objective := $Objective as Objective
@onready var spawner := $Spawner as Spawner
@onready var tower_slot_placer := $TowerSlotPlacer as Node2D

func _ready():
	# Apply dystopian atmosphere
	_apply_dystopian_atmosphere()

	# initialize camera
	var map_limits := tilemap.get_used_rect()
	var tile_size := tilemap.tile_set.tile_size
	camera.limit_left = map_limits.position.x * tile_size.x
	camera.limit_top = map_limits.position.y * tile_size.y
	camera.limit_right = map_limits.end.x * tile_size.x
	camera.limit_bottom = map_limits.end.y * tile_size.y
	# initialize money and connect signals
	var hud = camera.hud as HUD
	Global.money_changed.connect(hud._on_money_changed)
	Global.money = starting_money
	hud.initialize(objective.health)
	objective.health_changed.connect(hud._on_objective_health_changed)
	objective.objective_destroyed.connect(_on_objective_destroyed)
	spawner.countdown_started.connect(hud._on_spawner_countdown_started)
	spawner.wave_started.connect(hud._on_spawner_wave_started)
	spawner.enemy_spawned.connect(_on_enemy_spawned)
	spawner.enemies_defeated.connect(_on_enemies_defeated)

	
func _on_enemy_spawned(enemy: Enemy):
	enemy.enemy_died.connect(_on_enemy_died)


func _on_enemy_died(enemy: Enemy):
	Global.money += enemy.kill_reward


func _game_over():
	var hud = camera.hud as HUD
	hud.get_node("Menus/GameOver").enable()
	# Prevent pausing during game over screen
	hud.get_node("Menus/Pause").queue_free()


func _on_objective_destroyed():
	_game_over()


func _on_enemies_defeated():
	_game_over()


func _apply_dystopian_atmosphere():
	# Create a dark, dystopian atmosphere
	# Add a dark overlay to the entire map
	var overlay = ColorRect.new()
	overlay.color = Color(0.3, 0.2, 0.2, 0.4)  # Dark reddish tint
	overlay.size = Vector2(10000, 10000)  # Large enough to cover the map
	overlay.position = Vector2(-5000, -5000)  # Center it
	overlay.z_index = -1  # Behind everything else
	add_child(overlay)

	# Modify the tilemap modulation for a darker, more oppressive look
	tilemap.modulate = Color(0.7, 0.6, 0.6, 1.0)  # Darker, desaturated

	# Add some atmospheric particles or fog effect
	_create_atmospheric_effects()


func _create_atmospheric_effects():
	# Create a subtle particle system for dust/ash in the air
	var particles = CPUParticles2D.new()
	add_child(particles)

	# Position particles to cover the map
	particles.position = Vector2(2000, 2000)  # Center of map roughly
	particles.z_index = 10  # Above most things but below UI

	# Configure particle properties for dystopian dust/ash
	particles.emitting = true
	particles.amount = 50
	particles.lifetime = 8.0
	particles.texture = null  # Use default white square

	# Emission properties
	particles.emission_shape = CPUParticles2D.EMISSION_SHAPE_BOX
	particles.emission_box_extents = Vector3(3000, 2000, 0)  # Cover most of the map

	# Movement properties
	particles.direction = Vector3(1, -0.2, 0)  # Slight upward drift
	particles.initial_velocity_min = 10.0
	particles.initial_velocity_max = 30.0
	particles.gravity = Vector3(0, -5, 0)  # Very light upward movement

	# Appearance properties
	particles.scale_amount_min = 0.1
	particles.scale_amount_max = 0.3
	particles.color = Color(0.4, 0.3, 0.3, 0.3)  # Dark, semi-transparent

	# Add some randomness
	particles.angular_velocity_min = -30.0
	particles.angular_velocity_max = 30.0
