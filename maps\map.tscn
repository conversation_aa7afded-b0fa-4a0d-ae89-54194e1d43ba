[gd_scene load_steps=26 format=3 uid="uid://b4bsgajdug3qc"]

[ext_resource type="Script" uid="uid://corf0v1q0ykmd" path="res://maps/map.gd" id="1_dh14p"]
[ext_resource type="Texture2D" uid="uid://d06ljw7ag2tes" path="res://assets/tiles/field_tilesheet.png" id="1_vqxpx"]
[ext_resource type="PackedScene" uid="uid://2nav3a8prnbn" path="res://maps/objective/objective.tscn" id="3_l27ho"]
[ext_resource type="PackedScene" uid="uid://b0cf6ivwy8xat" path="res://maps/camera/camera2d.tscn" id="4_ugg6l"]
[ext_resource type="PackedScene" uid="uid://co0rdnv4yehse" path="res://maps/spawner/spawner.tscn" id="5_jhdu7"]
[ext_resource type="Script" uid="uid://clodtl2rm5lyy" path="res://maps/tower_slot_placer.gd" id="9_tower_slot_placer"]

[sub_resource type="NavigationPolygon" id="NavigationPolygon_g4rqo"]
vertices = PackedVector2Array(29.5, 64, -64, 64, -64, -64, 36, 38, 64, -64, 64, 27)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3), PackedInt32Array(3, 2, 4, 5)])
outlines = Array[PackedVector2Array]([PackedVector2Array(29.5, 64, -64, 64, -64, -64, 64, -64, 64, 27, 36, 38)])

[sub_resource type="NavigationPolygon" id="NavigationPolygon_1nuvq"]
vertices = PackedVector2Array(30, -64, 29, 64, -64, 64, -64, -64)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-64, -64, -64, 64, 29, 64, 30, -64)])

[sub_resource type="NavigationPolygon" id="NavigationPolygon_htayy"]
vertices = PackedVector2Array(64, -30, 64, 64, -64, 64, 39, -34, -64, -64, 27, -64)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3), PackedInt32Array(3, 2, 4, 5)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-64, -64, -64, 64, 64, 64, 64, -30, 39, -34, 27, -64)])

[sub_resource type="NavigationPolygon" id="NavigationPolygon_tew23"]
vertices = PackedVector2Array(64, -64, 64, 29.5, -64, 29.5, -64, -64)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-64, -64, -64, 29.5, 64, 29.5, 64, -64)])

[sub_resource type="NavigationPolygon" id="NavigationPolygon_megqg"]
vertices = PackedVector2Array(-64, -29.5, 64, -29, 64, 64, -64, 64)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-64, -29.5, 64, -29, 64, 64, -64, 64)])

[sub_resource type="NavigationPolygon" id="NavigationPolygon_l02k7"]
vertices = PackedVector2Array(-64, -64, 64, -64, 64, 64, -35.5, 34, -64, 32.5, -32.5, 64)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3), PackedInt32Array(3, 4, 0), PackedInt32Array(3, 2, 5)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-64, -64, -64, 32.5, -35.5, 34, -32.5, 64, 64, 64, 64, -64)])

[sub_resource type="NavigationPolygon" id="NavigationPolygon_ateg8"]
vertices = PackedVector2Array(64, -64, 64, 64, -29, 64, -27.5, -64)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-27.5, -64, -29, 64, 64, 64, 64, -64)])

[sub_resource type="NavigationPolygon" id="NavigationPolygon_75acs"]
vertices = PackedVector2Array(-21, -64, 64, -64, 64, 64, -64, 64, -40, -32, -64, -31.5)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3, 4), PackedInt32Array(3, 5, 4)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-64, -31.5, -40, -32, -21, -64, 64, -64, 64, 64, -64, 64)])

[sub_resource type="NavigationPolygon" id="NavigationPolygon_h302p"]
vertices = PackedVector2Array(-26, 64, -13.5, 8.5, 26.5, -21.5, 64, -28.5, 64, 64)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3, 4)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-26, 64, -13.5, 8.5, 26.5, -21.5, 64, -28.5, 64, 64)])

[sub_resource type="NavigationPolygon" id="NavigationPolygon_r05wx"]
vertices = PackedVector2Array(64, -64, 64, 30, 27, 21.5, -16.5, -10.5, -28.5, -64)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3, 4)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-28.5, -64, -16.5, -10.5, 27, 21.5, 64, 30, 64, -64)])

[sub_resource type="NavigationPolygon" id="NavigationPolygon_0jos6"]
vertices = PackedVector2Array(64, -64, 64, 64, -64, 64, -64, -64)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-64, -64, -64, 64, 64, 64, 64, -64)])

[sub_resource type="NavigationPolygon" id="NavigationPolygon_x0ptx"]
vertices = PackedVector2Array(-64, -30, 5, -6, 30.5, 64, -64, 64)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-64, -30, 5, -6, 30.5, 64, -64, 64)])

[sub_resource type="NavigationPolygon" id="NavigationPolygon_r2pis"]
vertices = PackedVector2Array(32.5, -64, 12, -16, -19.5, 17.5, -64, 29, -64, -64)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3, 4)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-64, -64, -64, 29, -19.5, 17.5, 12, -16, 32.5, -64)])

[sub_resource type="NavigationPolygon" id="NavigationPolygon_amtsx"]
vertices = PackedVector2Array(64, -64, 64, 64, -64, 64, -64, -64)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-64, -64, -64, 64, 64, 64, 64, -64)])

[sub_resource type="NavigationPolygon" id="NavigationPolygon_k6jh0"]
vertices = PackedVector2Array(-64, 64, -60, -64, 64, -64, 64, 64)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-64, 64, -60, -64, 64, -64, 64, 64)])

[sub_resource type="NavigationPolygon" id="NavigationPolygon_v83p8"]
vertices = PackedVector2Array(-64, 64, -64, -64, 64, -64, 64, 64)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-64, 64, -64, -64, 64, -64, 64, 64)])

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_j3s5g"]
texture = ExtResource("1_vqxpx")
texture_region_size = Vector2i(128, 128)
0:0/0 = 0
0:0/0/terrain_set = 0
0:0/0/terrain = 0
0:0/0/terrains_peering_bit/bottom_left_corner = 0
0:0/0/terrains_peering_bit/top_left_corner = 0
0:0/0/terrains_peering_bit/top_right_corner = 0
0:0/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_g4rqo")
1:0/0 = 0
1:0/0/terrain_set = 0
1:0/0/terrain = 0
1:0/0/terrains_peering_bit/top_left_corner = 0
1:0/0/terrains_peering_bit/top_right_corner = 0
1:0/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_tew23")
2:0/0 = 0
2:0/0/terrain_set = 0
2:0/0/terrain = 0
2:0/0/terrains_peering_bit/bottom_right_corner = 0
2:0/0/terrains_peering_bit/top_left_corner = 0
2:0/0/terrains_peering_bit/top_right_corner = 0
2:0/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_l02k7")
3:0/0 = 0
3:0/0/terrain_set = 0
3:0/0/terrains_peering_bit/bottom_right_corner = 0
3:0/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_h302p")
4:0/0 = 0
4:0/0/terrain_set = 0
4:0/0/terrains_peering_bit/bottom_left_corner = 0
4:0/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_x0ptx")
5:0/0 = 0
6:0/0 = 0
0:1/0 = 0
0:1/0/terrain_set = 0
0:1/0/terrain = 0
0:1/0/terrains_peering_bit/bottom_left_corner = 0
0:1/0/terrains_peering_bit/top_left_corner = 0
0:1/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_1nuvq")
1:1/0 = 0
1:1/0/terrain_set = 0
2:1/0 = 0
2:1/0/terrain_set = 0
2:1/0/terrain = 0
2:1/0/terrains_peering_bit/bottom_right_corner = 0
2:1/0/terrains_peering_bit/top_right_corner = 0
2:1/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_ateg8")
3:1/0 = 0
3:1/0/terrain_set = 0
3:1/0/terrains_peering_bit/top_right_corner = 0
3:1/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_r05wx")
4:1/0 = 0
4:1/0/terrain_set = 0
4:1/0/terrains_peering_bit/top_left_corner = 0
4:1/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_r2pis")
5:1/0 = 0
6:1/0 = 0
0:2/0 = 0
0:2/0/terrain_set = 0
0:2/0/terrain = 0
0:2/0/terrains_peering_bit/bottom_right_corner = 0
0:2/0/terrains_peering_bit/bottom_left_corner = 0
0:2/0/terrains_peering_bit/top_left_corner = 0
0:2/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_htayy")
1:2/0 = 0
1:2/0/terrain_set = 0
1:2/0/terrain = 0
1:2/0/terrains_peering_bit/bottom_right_corner = 0
1:2/0/terrains_peering_bit/bottom_left_corner = 0
1:2/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_megqg")
2:2/0 = 0
2:2/0/terrain_set = 0
2:2/0/terrain = 0
2:2/0/terrains_peering_bit/bottom_right_corner = 0
2:2/0/terrains_peering_bit/bottom_left_corner = 0
2:2/0/terrains_peering_bit/top_right_corner = 0
2:2/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_75acs")
3:2/0 = 0
3:2/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_0jos6")
4:2/0 = 0
4:2/0/terrain_set = 0
4:2/0/terrain = 0
4:2/0/terrains_peering_bit/bottom_right_corner = 0
4:2/0/terrains_peering_bit/bottom_left_corner = 0
4:2/0/terrains_peering_bit/top_left_corner = 0
4:2/0/terrains_peering_bit/top_right_corner = 0
4:2/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_amtsx")
5:2/0 = 0
6:2/0 = 0
0:3/0 = 0
1:3/0 = 0
2:3/0 = 0
3:3/0 = 0
4:3/0 = 0
5:3/0 = 0
5:3/0/terrain_set = 0
5:3/0/terrains_peering_bit/bottom_right_corner = 0
5:3/0/terrains_peering_bit/top_left_corner = 0
5:3/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_k6jh0")
6:3/0 = 0
6:3/0/terrain_set = 0
6:3/0/terrains_peering_bit/bottom_left_corner = 0
6:3/0/terrains_peering_bit/top_right_corner = 0
6:3/0/navigation_layer_0/polygon = SubResource("NavigationPolygon_v83p8")
0:4/0 = 0
1:4/0 = 0
2:4/0 = 0
3:4/0 = 0
4:4/0 = 0
5:4/0 = 0
6:4/0 = 0
0:5/0 = 0
1:5/0 = 0
2:5/0 = 0
3:5/0 = 0
4:5/0 = 0
5:5/0 = 0
6:5/0 = 0
0:6/0 = 0
1:6/0 = 0
2:6/0 = 0
3:6/0 = 0
4:6/0 = 0
5:6/0 = 0
6:6/0 = 0
0:7/0 = 0
1:7/0 = 0
2:7/0 = 0
3:7/0 = 0
4:7/0 = 0
5:7/0 = 0
6:7/0 = 0

[sub_resource type="TileSet" id="TileSet_pb8tk"]
tile_size = Vector2i(128, 128)
terrain_set_0/mode = 1
terrain_set_0/terrain_0/name = "Path"
terrain_set_0/terrain_0/color = Color(0.501961, 0.203922, 0.384314, 1)
navigation_layer_0/layers = 1
sources/0 = SubResource("TileSetAtlasSource_j3s5g")

[sub_resource type="NavigationPolygon" id="NavigationPolygon_nxq2u"]
vertices = PackedVector2Array(4724.01, -756, 4729.98, 2817.6, -247.008, 4337.49, -251.992, -760.977)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-262, -771, -257, 4351, 4741, 4349, 4734, -766)])

[node name="Map" type="Node2D"]
script = ExtResource("1_dh14p")

[node name="TileMap" type="TileMap" parent="."]
tile_set = SubResource("TileSet_pb8tk")
format = 2
layer_0/name = "Ground"
layer_0/tile_data = PackedInt32Array(-196602, 262144, 7, -131066, 65536, 5, -65530, 65536, 6, 393222, 65536, 1, 458758, 65536, 1, 524294, 65536, 1, 589830, 65536, 1, 655366, 65536, 1, 720902, 65536, 2, 786438, 262144, 2, -196601, 262144, 7, -131065, 65536, 5, -65529, 65536, 6, 7, 65536, 2, 65543, 262144, 2, 196615, 262144, 2, 393223, 65536, 1, 524295, 65536, 1, 589831, 65536, 1, 655367, 65536, 1, 720903, 65536, 2, 786439, 262144, 2, -196600, 0, 6, -131064, 262144, 6, -65528, 65536, 6, 8, 65536, 2, 65544, 262144, 2, 131080, 262144, 2, 393224, 65536, 1, 458760, 65536, 1, 524296, 65536, 1, 589832, 65536, 1, 655368, 65536, 1, 720904, 65536, 2, 786440, 262144, 2, -196599, 65536, 6, -131063, 65536, 6, -65527, 65536, 6, 196617, 262144, 2, 393225, 65536, 1, 524297, 65536, 1, 720905, 65536, 2, 786441, 262144, 2, -196598, 65536, 1, -65526, 65536, 1, 196618, 262144, 2, 393226, 65536, 1, 524298, 65536, 1, 589834, 65536, 1, 720906, 65536, 2, 786442, 262144, 2, -196597, 65536, 1, -131061, 65536, 1, -65525, 65536, 1, 393227, 65536, 1, 524299, 65536, 1, 589835, 65536, 1, 655371, 65536, 1, 786443, 262144, 2, -196596, 65536, 1, -131060, 65536, 1, -65524, 65536, 1, 196620, 262144, 2, 393228, 65536, 1, 524300, 65536, 1, 720908, 65536, 2, 786444, 262144, 2, -196595, 65536, 1, -131059, 65536, 1, -65523, 65536, 1, 196621, 262144, 2, 393229, 65536, 1, 458765, 65536, 1, 589837, 65536, 1, 655373, 65536, 1, 786445, 262144, 2, -196594, 65536, 1, -131058, 65536, 1, -65522, 65536, 1, 196622, 262144, 2, 720910, 65536, 2, 786446, 262144, 2, -196593, 65536, 1, -131057, 65536, 1, -65521, 65536, 1, 196623, 262144, 2, 393231, 65536, 1, 524303, 65536, 1, 655375, 65536, 1, 720911, 65536, 2, 786447, 262144, 2, -196592, 65536, 1, -131056, 65536, 1, -65520, 65536, 1, 196624, 262144, 2, 393232, 65536, 1, 524304, 65536, 1, 720912, 65536, 2, 786448, 262144, 2, -196591, 65536, 1, -131055, 65536, 1, -65519, 65536, 1, 196625, 262144, 2, 393233, 65536, 1, 524305, 131072, 6, 589841, 196608, 6, 655377, 65536, 1, 720913, 65536, 2, 786449, 262144, 2, -196590, 65536, 1, -131054, 65536, 1, -65518, 65536, 1, 196626, 262144, 2, 393234, 65536, 1, 458770, 65536, 7, 524306, 262144, 7, 589842, 65536, 5, 655378, 65536, 1, 720914, 65536, 2, 786450, 262144, 2, -196589, 65536, 1, -131053, 65536, 1, -65517, 65536, 1, 196627, 262144, 2, 393235, 65536, 1, 524307, 262144, 7, 589843, 65536, 5, 720915, 65536, 2, 786451, 262144, 2, -196588, 65536, 1, -131052, 65536, 1, -65516, 65536, 1, 196628, 262144, 2, 393236, 65536, 1, 524308, 0, 6, 589844, 262144, 6, 655380, 65536, 1, 720916, 65536, 2, 786452, 262144, 2, -196587, 65536, 1, -131051, 65536, 1, 196629, 262144, 2, 393237, 65536, 1, 720917, 65536, 2, 786453, 262144, 2, -196586, 65536, 1, -131050, 65536, 1, -65514, 65536, 1, 196630, 262144, 2, 458774, 65536, 1, 589846, 65536, 1, 655382, 65536, 1, 720918, 65536, 2, 786454, 262144, 2, -196585, 65536, 1, -131049, 65536, 1, -65513, 65536, 1, 393239, 65536, 1, 458775, 65536, 1, 524311, 65536, 1, 589847, 65536, 1, 655383, 65536, 1, 720919, 65536, 2, 786455, 262144, 2, -196584, 65536, 1, -131048, 65536, 1, -65512, 65536, 1, 196632, 262144, 2, 393240, 65536, 1, 458776, 65536, 1, 524312, 65536, 1, 589848, 65536, 1, 655384, 65536, 1, 786456, 262144, 2, -196583, 65536, 1, -131047, 65536, 1, -65511, 65536, 1, 25, 65536, 2, 458777, 196608, 0, 524313, 131072, 1, 589849, 131072, 1, 655385, 131072, 1, 720921, 131072, 2, 786457, 262144, 2, -196582, 65536, 1, -131046, 65536, 1, -65510, 65536, 1, 26, 65536, 2, 65562, 262144, 2, 393242, 65536, 1, 524314, 262144, 2, 589850, 262144, 2, 655386, 262144, 2, 786458, 262144, 2, -196581, 65536, 1, -131045, 65536, 1, 131099, 262144, 2, 393243, 65536, 1, 458779, 65536, 2, 524315, 262144, 2, 589851, 262144, 2, 655387, 262144, 2, 720923, 262144, 2, 786459, 262144, 2, -196580, 65536, 1, -131044, 65536, 1, -65508, 65536, 1, 28, 65536, 2, 65564, 262144, 2, 131100, 262144, 2, 196636, 262144, 2, 393244, 131072, 1, 524316, 262144, 2, 589852, 262144, 2, 655388, 262144, 2, 720924, 262144, 2, 786460, 262144, 2, -196579, 65536, 1, -131043, 65536, 1, -65507, 65536, 1, 65565, 262144, 2, 131101, 262144, 2, 196637, 262144, 2, 393245, 262144, 2, 458781, 262144, 2, 524317, 262144, 2, 589853, 262144, 2, 720925, 262144, 2, 786461, 262144, 2, -196578, 65536, 1, -131042, 65536, 1, -65506, 65536, 1, 30, 65536, 2, 65566, 262144, 2, 131102, 262144, 2, 196638, 262144, 2, 393246, 262144, 2, 524318, 262144, 2, 589854, 262144, 2, 655390, 262144, 2, 720926, 262144, 2, 786462, 262144, 2, -131041, 65536, 1, -65505, 65536, 1, 31, 65536, 2, 65567, 262144, 2, 393247, 262144, 2, 458783, 262144, 2, 524319, 262144, 2, 589855, 262144, 2, 655391, 262144, 2, 720927, 262144, 2, 786463, 262144, 2, -196576, 65536, 1, 32, 65536, 2, 65568, 262144, 2, 131104, 262144, 2, 196640, 262144, 2, 262176, 262144, 2, 327712, 262144, 2, 393248, 262144, 2, 458784, 262144, 2, 524320, 262144, 2, 589856, 262144, 2, 655392, 262144, 2, 720928, 262144, 2, 786464, 262144, 2, -196575, 65536, 1, -131039, 65536, 1, -65503, 65536, 1, 65569, 0, 1, 131105, 0, 1, 196641, 0, 1, 262177, 0, 1, 393249, 0, 1, 458785, 0, 1, 589857, 0, 1, 655393, 0, 1, 720929, 0, 1, 196616, 262144, 2, 458762, 65536, 1, 458764, 65536, 1, 458767, 65536, 1, 458763, 65536, 1, 458759, 65536, 1, 458768, 65536, 1, 458772, 262144, 5, 458761, 65536, 1, 524310, 65536, 1, 393238, 65536, 1, 720907, 65536, 2, 720909, 65536, 2, 589840, 65536, 1, 655376, 65536, 1, 589833, 65536, 1, 655369, 65536, 1, 524302, 65536, 1, 458766, 65536, 1, 589836, 65536, 1, 655370, 65536, 1, 655372, 65536, 1, 655374, 65536, 1, 589839, 65536, 1, -65515, 65536, 1, -65531, 65536, 6, -131067, 65536, 5, -65532, 65536, 6, -131068, 65536, 5, -65533, 65536, 6, -131069, 65536, 5, -65534, 65536, 6, -131070, 65536, 5, -65535, 65536, 6, -131071, 65536, 5, -65536, 65536, 6, -1, 65536, 6, 0, 65536, 6, 1, 196608, 0, 65535, 65536, 6, 65536, 65536, 6, 131071, 65536, 6, 65537, 131072, 1, 131072, 65536, 6, 196607, 65536, 6, 131073, 131072, 1, 131074, 262144, 2, 65538, 262144, 2, 2, 65536, 2, 3, 65536, 2, 4, 65536, 2, 5, 65536, 2, 65540, 262144, 2, 65539, 262144, 2, 65541, 262144, 2, 131077, 262144, 2, 131076, 262144, 2, 131075, 262144, 2, 196608, 65536, 6, 196609, 131072, 1, 262143, 65536, 6, 196610, 262144, 2, 196611, 262144, 2, 196613, 262144, 2, 262144, 65536, 6, 327680, 65536, 6, 655365, 65536, 1, 589829, 65536, 1, 655364, 0, 1, 589827, 262144, 2, 655362, 262144, 2, 589826, 262144, 2, 720898, 262144, 2, 524290, 262144, 2, 524291, 262144, 2, 458754, 262144, 2, 458755, 262144, 2, 393218, 262144, 2, 393220, 0, 1, 393221, 65536, 1, 458756, 0, 1, 458757, 65536, 1, 524292, 0, 1, 524293, 65536, 1, 720900, 0, 2, 720901, 65536, 2, 327679, 65536, 6, 393215, 65536, 1, 524289, 131072, 1, 589825, 131072, 1, 655360, 65536, 1, 720895, 65536, 1, 655359, 65536, 1, 589823, 65536, 1, 524287, 65536, 1, 458751, 65536, 1, 393216, 65536, 1, 458752, 65536, 1, 524288, 65536, 1, 589824, 65536, 1, 786431, 65536, 1, 851967, 65536, 1, 786432, 65536, 1, 786433, 131072, 1, 786434, 262144, 2, 786437, 262144, 2, 720897, 131072, 1, 720896, 65536, 1, -196608, 131072, 6, -196607, 262144, 7, -196606, 262144, 7, -196603, 262144, 7, -196604, 196608, 7, -196605, 262144, 7, -262144, 196608, 5, 1835008, 65536, 1, 851969, 131072, 1, 1835009, 131072, 1, 851970, 262144, 2, 1835010, 262144, 2, 1835011, 262144, 2, 1835012, 65536, 2, 851973, 65536, 0, 917509, 65536, 1, 983045, 65536, 1, 1048581, 65536, 1, 1114117, 65536, 1, 1179653, 65536, 1, 1245189, 65536, 1, 1376261, 262144, 2, 1441797, 65536, 0, 1507333, 65536, 1, 1572869, 65536, 1, 1703941, 65536, 1, 1769477, 65536, 1, 1835013, 65536, 2, 851974, 65536, 0, 917510, 65536, 1, 983046, 65536, 1, 1048582, 65536, 1, 1114118, 65536, 1, 1179654, 65536, 1, 1245190, 65536, 1, 1310726, 65536, 2, 1572870, 65536, 1, 1703942, 65536, 1, 851975, 65536, 0, 917511, 65536, 1, 983047, 65536, 1, 1048583, 65536, 1, 1114119, 65536, 1, 1179655, 65536, 1, 1245191, 65536, 1, 1310727, 65536, 2, 1572871, 65536, 1, 1703943, 65536, 1, 1769479, 65536, 1, 851976, 65536, 0, 917512, 65536, 1, 983048, 65536, 1, 1048584, 65536, 1, 1114120, 65536, 1, 1179656, 65536, 1, 1245192, 65536, 1, 1310728, 65536, 2, 1376264, 262144, 2, 1572872, 65536, 1, 1703944, 65536, 1, 1835016, 65536, 2, 851977, 65536, 0, 917513, 65536, 1, 983049, 65536, 1, 1048585, 65536, 1, 1114121, 65536, 1, 1179657, 65536, 1, 1245193, 65536, 1, 1310729, 65536, 2, 1703945, 65536, 1, 1835017, 65536, 2, 851978, 65536, 0, 917514, 65536, 1, 1114122, 65536, 1, 1179658, 65536, 1, 1245194, 65536, 1, 1310730, 65536, 2, 1703946, 65536, 1, 1769482, 65536, 1, 851979, 65536, 0, 917515, 65536, 1, 1114123, 65536, 1, 1310731, 65536, 2, 1572875, 65536, 1, 1703947, 65536, 1, 851980, 65536, 0, 917516, 65536, 1, 983052, 65536, 1, 1114124, 65536, 1, 1179660, 65536, 1, 1310732, 65536, 2, 1572876, 65536, 1, 1703948, 65536, 1, 1835020, 65536, 2, 851981, 65536, 0, 917517, 65536, 1, 983053, 65536, 1, 1179661, 65536, 1, 1245197, 65536, 1, 1310733, 65536, 2, 1572877, 65536, 1, 1703949, 65536, 1, 1769485, 65536, 1, 1835021, 65536, 2, 851982, 65536, 0, 917518, 65536, 1, 983054, 65536, 1, 1048590, 65536, 1, 1114126, 65536, 1, 1179662, 65536, 1, 1245198, 65536, 1, 1310734, 65536, 2, 1572878, 65536, 1, 1769486, 65536, 1, 851983, 65536, 0, 917519, 65536, 1, 983055, 65536, 1, 1048591, 65536, 1, 1114127, 65536, 1, 1179663, 65536, 1, 1245199, 65536, 1, 1310735, 65536, 2, 1572879, 65536, 1, 1703951, 65536, 1, 1769487, 65536, 1, 851984, 65536, 0, 917520, 65536, 1, 983056, 65536, 1, 1114128, 65536, 1, 1179664, 65536, 1, 1245200, 65536, 1, 1310736, 65536, 2, 1572880, 65536, 1, 1703952, 65536, 1, 1769488, 65536, 1, 1835024, 65536, 2, 851985, 65536, 0, 917521, 65536, 1, 983057, 65536, 1, 1048593, 65536, 1, 1114129, 65536, 1, 1179665, 65536, 1, 1245201, 65536, 1, 1310737, 65536, 2, 1572881, 65536, 1, 1703953, 65536, 1, 1769489, 65536, 1, 1835025, 65536, 2, 851986, 131072, 0, 917522, 131072, 1, 983058, 131072, 1, 1048594, 131072, 1, 1114130, 131072, 1, 1179666, 131072, 1, 1245202, 131072, 1, 1310738, 131072, 2, 1703954, 65536, 1, 1769490, 65536, 1, 1835026, 65536, 2, 851987, 262144, 2, 917523, 262144, 2, 983059, 262144, 2, 1048595, 262144, 2, 1245203, 262144, 2, 1703955, 65536, 1, 1769491, 65536, 1, 1835027, 65536, 2, 851988, 262144, 2, 917524, 262144, 2, 983060, 262144, 2, 1048596, 262144, 2, 1114132, 262144, 2, 1179668, 262144, 2, 1245204, 262144, 2, 1310740, 262144, 2, 1572884, 65536, 1, 1703956, 65536, 1, 1769492, 65536, 1, 851989, 0, 0, 917525, 0, 1, 983061, 0, 1, 1048597, 0, 1, 1179669, 0, 1, 1245205, 0, 1, 1310741, 0, 1, 1572885, 65536, 1, 1769493, 65536, 1, 851990, 65536, 0, 917526, 65536, 1, 1048598, 65536, 1, 1114134, 65536, 1, 1179670, 65536, 1, 1572886, 65536, 1, 1769494, 65536, 1, 851991, 65536, 0, 917527, 65536, 1, 983063, 65536, 1, 1048599, 65536, 1, 1114135, 65536, 1, 1179671, 65536, 1, 1245207, 65536, 1, 1310743, 65536, 1, 1507351, 65536, 1, 1638423, 65536, 1, 1703959, 65536, 1, 1769495, 65536, 1, 851992, 65536, 0, 917528, 65536, 1, 983064, 65536, 1, 1048600, 65536, 1, 1114136, 65536, 1, 1179672, 65536, 1, 1245208, 65536, 1, 1310744, 65536, 1, 1376280, 65536, 1, 1441816, 65536, 1, 1507352, 65536, 1, 1703960, 65536, 1, 1769496, 65536, 1, 917529, 131072, 1, 983065, 196608, 1, 1048601, 65536, 1, 1179673, 65536, 1, 1245209, 65536, 1, 1310745, 65536, 1, 1376281, 65536, 1, 1507353, 65536, 1, 1572889, 65536, 1, 1638425, 65536, 1, 1703961, 65536, 1, 1769497, 65536, 1, 851994, 262144, 2, 917530, 262144, 2, 983066, 65536, 0, 1048602, 65536, 1, 1114138, 65536, 1, 1179674, 65536, 1, 1245210, 65536, 1, 1310746, 65536, 1, 1376282, 65536, 1, 1441818, 65536, 1, 1507354, 65536, 1, 1572890, 65536, 1, 1703962, 65536, 1, 1769498, 65536, 1, 851995, 262144, 2, 917531, 262144, 2, 983067, 65536, 0, 1179675, 65536, 1, 1310747, 65536, 1, 1376283, 65536, 1, 1441819, 65536, 1, 1507355, 65536, 1, 1572891, 65536, 1, 1638427, 65536, 1, 1703963, 65536, 1, 1769499, 65536, 1, 1835035, 65536, 2, 851996, 262144, 2, 917532, 262144, 2, 983068, 65536, 0, 1048604, 65536, 1, 1114140, 65536, 1, 1179676, 65536, 1, 1245212, 65536, 1, 1310748, 65536, 1, 1376284, 65536, 1, 1441820, 65536, 1, 1507356, 65536, 1, 1572892, 65536, 1, 1703964, 65536, 1, 1769500, 65536, 1, 1835036, 65536, 2, 851997, 262144, 2, 917533, 262144, 2, 983069, 65536, 0, 1048605, 65536, 1, 1114141, 65536, 1, 1179677, 65536, 1, 1245213, 65536, 1, 1310749, 65536, 1, 1376285, 65536, 1, 1441821, 65536, 1, 1507357, 65536, 1, 1572893, 65536, 1, 1638429, 65536, 1, 1703965, 65536, 1, 1835037, 65536, 2, 851998, 262144, 2, 917534, 262144, 2, 1048606, 131072, 1, 1114142, 131072, 1, 1179678, 131072, 1, 1245214, 131072, 1, 1310750, 131072, 1, 1376286, 131072, 1, 1441822, 131072, 1, 1507358, 131072, 1, 1703966, 131072, 1, 1769502, 131072, 1, 1835038, 131072, 2, 851999, 262144, 2, 917535, 262144, 2, 983071, 262144, 2, 1048607, 262144, 2, 1114143, 262144, 2, 1179679, 262144, 2, 1245215, 262144, 2, 1310751, 262144, 2, 1376287, 262144, 2, 1441823, 262144, 2, 1507359, 262144, 2, 1572895, 262144, 2, 1769503, 262144, 2, 1835039, 262144, 2, 852000, 262144, 2, 917536, 262144, 2, 1245216, 262144, 2, 1310752, 262144, 2, 1376288, 262144, 2, 1441824, 262144, 2, 1507360, 262144, 2, 1572896, 262144, 2, 1638432, 262144, 2, 1703968, 262144, 2, 1769504, 262144, 2, 1835040, 262144, 2, 852001, 0, 1, 917537, 0, 1, 983073, 0, 1, -131038, 65536, 1, -65502, 65536, 1, 34, 65536, 1, 65570, 65536, 1, 131106, 65536, 1, 196642, 65536, 1, 262178, 65536, 1, 327714, 65536, 1, 393250, 65536, 1, 458786, 65536, 1, 589858, 65536, 1, 786466, 65536, 1, 852002, 65536, 1, 917538, 65536, 1, 983074, 65536, 1, 1048610, 65536, 1, 1114146, 65536, 1, 1179682, 65536, 1, 1245218, 65536, 1, 1310754, 65536, 1, 1441826, 65536, 1, 1507362, 65536, 1, 1572898, 65536, 1, 1638434, 65536, 1, 1703970, 65536, 1, 1769506, 65536, 1, 1835042, 65536, 1, -196573, 65536, 1, -131037, 65536, 1, -65501, 65536, 1, 35, 65536, 1, 65571, 65536, 1, 131107, 65536, 1, 196643, 65536, 1, 262179, 65536, 1, 327715, 65536, 1, 393251, 65536, 1, 458787, 65536, 1, 524323, 65536, 1, 589859, 65536, 1, 655395, 65536, 1, 720931, 65536, 1, 786467, 65536, 1, 852003, 65536, 1, 917539, 65536, 1, 983075, 65536, 1, 1048611, 65536, 1, 1114147, 65536, 1, 1179683, 65536, 1, 1245219, 65536, 1, 1310755, 65536, 1, 1376291, 65536, 1, 1441827, 65536, 1, 1507363, 65536, 1, 1572899, 65536, 1, 1638435, 65536, 1, 1703971, 65536, 1, 1769507, 65536, 1, 1835043, 65536, 1, 131070, 65536, 6, 196606, 65536, 6, 262142, 65536, 6, 327678, 65536, 6, 393214, 65536, 1, 458750, 65536, 1, 524286, 65536, 1, 589822, 65536, 1, 655358, 65536, 1, 720894, 65536, 1, 786430, 65536, 1, 851966, 65536, 1, 983038, 65536, 1, 1048574, 65536, 1, 1114110, 65536, 1, 1179646, 65536, 1, 1245182, 65536, 1, 1310718, 65536, 1, 1376254, 65536, 1, 1441790, 65536, 1, 1507326, 65536, 1, 1572862, 65536, 1, 1638398, 65536, 1, 1703934, 65536, 1, 1769470, 65536, 1, 1835006, 65536, 1, 1900542, 65536, 1, 1966078, 65536, 1, 1048575, 65536, 1, 1114111, 65536, 1, 1179647, 65536, 1, 1245183, 65536, 1, 1310719, 65536, 1, 1376255, 65536, 1, 1572863, 65536, 1, 1638399, 65536, 1, 1703935, 65536, 1, 1769471, 65536, 1, 1900544, 65536, 1, 1900546, 262144, 2, 1900573, 262144, 2, 1900574, 262144, 2, 1900575, 262144, 2, 1900576, 262144, 2, 1900577, 0, 1, -262110, 65536, 1, -262109, 65536, 1, 1900579, 65536, 1, 786465, 0, 1, 458782, 262144, 2, 655389, 262144, 2, 655394, 65536, 1, 720922, 262144, 2, 458780, 131072, 2, 524321, 0, 1, 1114131, 262144, 2, 983072, 262144, 2, 1048608, 262144, 2, 1114144, 262144, 2, 1179680, 262144, 2, 1114145, 0, 1, 1179681, 0, 1, 1048609, 0, 1, 983070, 131072, 0, 1114125, 65536, 1, 720899, 262144, 2, 655363, 262144, 2, 786435, 262144, 2, 851971, 262144, 2, 1703967, 262144, 2, 1769501, 65536, 1, 1835034, 65536, 2, 1835033, 65536, 2, 1835032, 65536, 2, 1835031, 65536, 2, 1835030, 65536, 2, 1835029, 65536, 2, 1835028, 65536, 2, 1835041, 0, 1, 1769505, 0, 1, 1703969, 0, 1, 1638433, 0, 1, 1572897, 0, 1, 1507361, 0, 1, 1376289, 0, 1, 1310753, 0, 1, 1441825, 0, 1, 1245217, 0, 1, 1376290, 65536, 1, 393217, 131072, 1, 327681, 131072, 1, 327682, 262144, 2, 393219, 262144, 2, 262145, 131072, 1, 262146, 262144, 2, 262174, 262144, 2, 262173, 262144, 2, 327710, 262144, 2, 327709, 262144, 2, 786436, 262144, 2, 720920, 65536, 2, 851993, 131072, 0, 851972, 0, 0, -131062, 65536, 1, 131103, 262144, 2, 196639, 262144, 2, 262175, 262144, 2, 327711, 262144, 2, -196577, 65536, 1, 327683, 262144, 2, 262147, 262144, 2, 655361, 131072, 1, 458753, 131072, 1, -65509, 65536, 1, -131040, 65536, 1, -65504, 65536, 1, 1900547, 262144, 2, 1900545, 131072, 1, 1966079, 65536, 1, 1900548, 262144, 2, 1703957, 65536, 1, 1703958, 65536, 1, 1900549, 262144, 2, 1900550, 262144, 2, 1900551, 262144, 2, 1900552, 262144, 2, 1900554, 262144, 2, 1900555, 262144, 2, 1900556, 262144, 2, 1900557, 262144, 2, 1900558, 262144, 2, 1900559, 262144, 2, 1900560, 262144, 2, 1900561, 262144, 2, 1900562, 262144, 2, 1900563, 262144, 2, 1900564, 262144, 2, 1900565, 262144, 2, 1900566, 262144, 2, 1900567, 262144, 2, 1900568, 262144, 2, 1900569, 262144, 2, 1900570, 262144, 2, 1900571, 262144, 2, 1900572, 262144, 2, 1966115, 65536, 1, 1966114, 65536, 1, 1966113, 262144, 1, 1966112, 65536, 0, 1966111, 65536, 0, 1966110, 65536, 0, 1966109, 65536, 0, 1966108, 65536, 0, 1966107, 65536, 0, 1966106, 65536, 0, 1966105, 65536, 0, 1966104, 65536, 0, 1966103, 65536, 0, 1966102, 65536, 0, 1966101, 65536, 0, 1966100, 65536, 0, 1966098, 65536, 0, 1966097, 65536, 0, 1966096, 65536, 0, 1966095, 65536, 0, 1966094, 65536, 0, 1966093, 65536, 0, 1966092, 65536, 0, 1966091, 65536, 0, 1966090, 65536, 0, 1966089, 65536, 0, 1966088, 65536, 0, 1966087, 65536, 0, 1966086, 65536, 0, 1966085, 65536, 0, 1966084, 65536, 0, 1966083, 65536, 0, 1966082, 65536, 0, 1966081, 196608, 1, 1966080, 65536, 1, 2031615, 65536, 1, 2031614, 65536, 1, 2031651, 65536, 1, 2031650, 65536, 1, 2031649, 65536, 1, 2031648, 65536, 1, 2031647, 65536, 1, 2031646, 65536, 1, 2031645, 65536, 1, 2031644, 65536, 1, 2031643, 65536, 1, 2031642, 65536, 1, 2031641, 65536, 1, 2031640, 65536, 1, 2031639, 65536, 1, 2031638, 65536, 1, 2031637, 65536, 1, 2031636, 65536, 1, 2031635, 65536, 1, 2031634, 65536, 1, 2031633, 65536, 1, 2031632, 65536, 1, 2031631, 65536, 1, 2031630, 65536, 1, 2031629, 65536, 1, 2031628, 65536, 1, 2031627, 65536, 1, 2097150, 65536, 1, 2097151, 65536, 1, 2031616, 65536, 1, 2031617, 65536, 1, 2031618, 65536, 1, 2031619, 65536, 1, 2031620, 65536, 1, 2031621, 65536, 1, 2031622, 65536, 1, 2031623, 65536, 1, 2031624, 65536, 1, 2031625, 65536, 1, 2031626, 65536, 1, 1245196, 65536, 1, 1245195, 65536, 1, 589828, 0, 1, 262148, 0, 0, 327685, 65536, 1, 327687, 65536, 1, 327688, 65536, 1, 327695, 65536, 1, 327696, 65536, 1, 327697, 65536, 1, 327698, 65536, 1, 327699, 65536, 1, 327700, 65536, 1, 327701, 65536, 1, 327702, 65536, 1, 327703, 65536, 1, 327704, 65536, 1, 327705, 65536, 1, 262168, 65536, 0, 262167, 65536, 0, 262166, 65536, 0, 262165, 65536, 0, 262164, 65536, 0, 262163, 65536, 0, 262162, 65536, 0, 262161, 65536, 0, 262160, 65536, 0, 262159, 65536, 0, 262158, 65536, 0, 262157, 65536, 0, 262156, 65536, 0, 262155, 65536, 0, 262154, 65536, 0, 262153, 65536, 0, 327689, 65536, 1, 327690, 65536, 1, 327691, 65536, 1, 327692, 65536, 1, 327693, 65536, 1, 327694, 65536, 1, 327684, 0, 1, 327713, 0, 1, 524322, 65536, 1, 720930, 65536, 1, -262146, 65536, 1, -262145, 65536, 1, -327680, 65536, 1, -327679, 131072, 6, -327678, 0, 6, -327677, 65536, 1, -327676, 65536, 1, -327675, 65536, 1, -327674, 65536, 1, -327673, 65536, 1, -327672, 65536, 1, -327671, 65536, 1, -327670, 65536, 1, -327669, 65536, 1, -327668, 65536, 1, -327667, 65536, 1, -327666, 65536, 1, -327665, 65536, 1, -327664, 65536, 1, -327663, 65536, 1, -327662, 65536, 1, -327682, 65536, 1, -327681, 65536, 1, -393216, 65536, 1, -393214, 0, 6, -393213, 65536, 1, -393212, 65536, 1, -393211, 65536, 1, -393210, 65536, 1, -393209, 65536, 1, -393208, 65536, 1, -393207, 65536, 1, -393206, 65536, 1, -393205, 65536, 1, -393204, 65536, 1, -393203, 65536, 1, -393202, 65536, 1, -393201, 65536, 1, -393200, 65536, 1, -393199, 65536, 1, -393198, 65536, 1, -393197, 65536, 1, -393196, 65536, 1, -393195, 65536, 1, -393194, 65536, 1, -393193, 65536, 1, -393192, 65536, 1, -393191, 65536, 1, -393190, 65536, 1, -393189, 65536, 1, -393188, 65536, 1, -393187, 65536, 1, -393186, 65536, 1, -393185, 65536, 1, -393184, 65536, 1, -393183, 65536, 1, -393182, 65536, 1, -393181, 65536, 1, 1441798, 65536, 0, 1441799, 65536, 0, 1376262, 262144, 2, 1376263, 262144, 2, 1441800, 65536, 0, 1441801, 65536, 0, 1441802, 65536, 0, 1441803, 65536, 0, 1441804, 65536, 0, 1441805, 65536, 0, 1441806, 65536, 0, 1441807, 65536, 0, 1441808, 65536, 0, 1441809, 65536, 0, 1376273, 262144, 2, 1441810, 65536, 0, 1376274, 262144, 2, 1507345, 65536, 1, 1507344, 65536, 1, 1507346, 65536, 1, 1507347, 65536, 1, 1507348, 65536, 1, 1441812, 65536, 0, 1507334, 65536, 1, 1507335, 65536, 1, 1507336, 65536, 1, 1507337, 65536, 1, 1507338, 65536, 1, 1507339, 65536, 1, 1507340, 65536, 1, 1507341, 65536, 1, 1507342, 65536, 1, 1507343, 65536, 1, 1507349, 65536, 1, 1441813, 262144, 1, 1376276, 262144, 2, 1376277, 0, 1, 1179659, 65536, 1, 1048586, 65536, 1, 1048587, 65536, 1, 1048588, 65536, 1, 1048589, 65536, 1, 1048592, 65536, 1, 1114137, 65536, 1, 1114139, 65536, 1, 1179667, 262144, 2, 1572883, 65536, 1, 1572894, 131072, 1, 1638431, 262144, 2, 1638430, 131072, 1, 1572882, 65536, 1, 1507350, 65536, 1, 1572887, 65536, 1, 1441815, 65536, 1, 1441817, 65536, 1, 1376265, 262144, 2, 1572873, 65536, 1, 1376266, 262144, 2, 1572874, 65536, 1, 1376267, 262144, 2, 1376268, 262144, 2, 1376269, 262144, 2, 1376270, 262144, 2, 1376271, 262144, 2, 1376272, 262144, 2, 1376279, 65536, 1, 1572888, 65536, 1, 1966099, 65536, 0, 983050, 65536, 1, 983051, 65536, 1, 983062, 65536, 1, 1048603, 65536, 1, 1245211, 65536, 1, 1900553, 262144, 2, 1769484, 65536, 1, 1769483, 65536, 1, 1769481, 65536, 1, 1769480, 65536, 1, 1769478, 65536, 1, 1638422, 65536, 1, 1638406, 65536, 1, 1638407, 65536, 1, 1638408, 65536, 1, 1638409, 65536, 1, 1638405, 65536, 1, 1638410, 65536, 1, 1638411, 65536, 1, 1638412, 65536, 1, 1638413, 65536, 1, 1638414, 65536, 1, 1638415, 65536, 1, 1638416, 65536, 1, 1638417, 65536, 1, 1638418, 65536, 1, 1638419, 65536, 1, 1638420, 65536, 1, 1638421, 65536, 1, 1638424, 65536, 1, 1638426, 65536, 1, 1638428, 65536, 1, 1835022, 65536, 2, 1835023, 65536, 2, 1835014, 65536, 2, 1835015, 65536, 2, 1835018, 65536, 2, 1835019, 65536, 2, 1703950, 65536, 1, -327645, 65536, 1, -327646, 65536, 1, -327647, 65536, 1, -327648, 65536, 1, -327649, 65536, 1, -327650, 65536, 1, -327651, 65536, 1, -327652, 65536, 1, -327653, 65536, 1, -327654, 65536, 1, -327655, 65536, 1, -327656, 65536, 1, -327657, 65536, 1, -327658, 65536, 1, -327659, 65536, 1, -327660, 65536, 1, -327661, 65536, 1, 917506, 262144, 2, 917505, 131072, 1, 917504, 65536, 1, 851968, 65536, 1, 983041, 131072, 1, 983042, 262144, 2, 983040, 65536, 1, 1048577, 131072, 1, 1048576, 65536, 1, 1114113, 131072, 1, 1114114, 262144, 2, 1114112, 65536, 1, 1179649, 131072, 1, 1179648, 65536, 1, 1245185, 131072, 1, 1245186, 262144, 2, 1245184, 65536, 1, 1310721, 131072, 1, 1310720, 65536, 1, 1376257, 131072, 1, 1376258, 262144, 2, 1441793, 131072, 1, 1441794, 262144, 2, 1507329, 131072, 1, 1507330, 262144, 2, 1507328, 65536, 1, 1572865, 131072, 1, 1572866, 262144, 2, 1572864, 65536, 1, 1638401, 131072, 1, 1638402, 262144, 2, 1638400, 65536, 1, 1703937, 131072, 1, 1703938, 262144, 2, 1703936, 65536, 1, 1769473, 131072, 1, 1769472, 65536, 1, 1769474, 262144, 2, 1310722, 262144, 2, 1179650, 262144, 2, 1048578, 262144, 2, 1048579, 262144, 2, 983043, 262144, 2, 983039, 65536, 1, 1376256, 65536, 1, 1441791, 65536, 1, 1441792, 65536, 1, 1507327, 65536, 1, 917503, 65536, 1, 917507, 262144, 2, 917508, 0, 1, 917502, 65536, 1, 65534, 65536, 6, -262143, 131072, 7, -262142, 0, 7, -2, 65536, 6, 393241, 65536, 1, 458778, 65536, 2, 196631, 262144, 2, 65563, 262144, 2, 327707, 65536, 1, 24, 65536, 2, 131098, 262144, 2, 196634, 262144, 2, 196635, 262144, 2, 262171, 65536, 0, 262172, 131072, 0, 327708, 131072, 1, 23, 65536, 2, 131097, 262144, 2, 27, 65536, 2, 262170, 65536, 0, 196633, 262144, 2, 65561, 262144, 2, 131082, 262144, 2, 131081, 262144, 2, 131083, 262144, 2, 65545, 262144, 2, 131084, 262144, 2, 131085, 262144, 2, 131086, 262144, 2, 131087, 262144, 2, 131088, 262144, 2, 131089, 262144, 2, 131090, 262144, 2, 131091, 262144, 2, 131092, 262144, 2, 131094, 262144, 2, 131095, 262144, 2, 458769, 196608, 5, 393230, 65536, 1, 524301, 65536, 1, 131079, 262144, 2, 262169, 65536, 0, 131096, 262144, 2, 9, 65536, 2, 1900543, 65536, 1, 1441795, 262144, 2, 1376259, 262144, 2, 1441796, 65536, 0, 1376260, 262144, 2, 1310739, 262144, 2, 1376275, 262144, 2, 1441811, 65536, 0, 1310724, 0, 2, 1310723, 262144, 2, 1245187, 262144, 2, 1245188, 0, 1, 1310725, 65536, 2, 1114133, 0, 1, 458771, 65536, 7, 655379, 65536, 1, 655381, 65536, 1, 589845, 65536, 1, 524309, 65536, 1, 458773, 65536, 1, 262149, 65536, 0, 589838, 65536, 1, 10, 65536, 2, 65546, 262144, 2, 11, 65536, 2, 65547, 262144, 2, 12, 65536, 2, 65548, 262144, 2, 13, 65536, 2, 65549, 262144, 2, 14, 65536, 2, 65550, 262144, 2, 15, 65536, 2, 65551, 262144, 2, 16, 65536, 2, 65552, 262144, 2, 17, 65536, 2, 65553, 262144, 2, 18, 65536, 2, 65554, 262144, 2, 19, 65536, 2, 65555, 262144, 2, 20, 65536, 2, 65556, 262144, 2, 21, 65536, 2, 22, 65536, 2, 65558, 262144, 2, 65559, 262144, 2, 65560, 262144, 2, 327706, 65536, 1, 1048580, 0, 1, 1114116, 0, 1, 1179652, 0, 1, 983044, 0, 1, 1114115, 262144, 2, 1179651, 262144, 2, 1572868, 65536, 1, 1572867, 262144, 2, 1638403, 262144, 2, 1703939, 262144, 2, 1703940, 65536, 1, 1638404, 65536, 1, 1507331, 262144, 2, 1769476, 65536, 1, 1769475, 262144, 2, 1507332, 65536, 1, 1245206, 65536, 1, 1310742, 65536, 1, 1376278, 65536, 1, 1441814, 65536, 1, -262141, 65536, 7, -262140, 65536, 7, -262139, 65536, 7, -262138, 65536, 7, -262137, 65536, 7, -262136, 262144, 5, -262135, 65536, 1, -262134, 65536, 1, -262133, 65536, 1, -262132, 65536, 1, -262131, 65536, 1, -262130, 65536, 1, -262129, 65536, 1, -262128, 65536, 1, -262127, 65536, 1, -262126, 65536, 1, -262125, 65536, 1, -262124, 65536, 1, -262123, 65536, 1, -262122, 65536, 1, -262121, 65536, 1, -262120, 65536, 1, -262119, 65536, 1, -262118, 65536, 1, -262117, 65536, 1, -262116, 65536, 1, -262115, 65536, 1, -262114, 65536, 1, -262113, 65536, 1, -262112, 65536, 1, -262111, 65536, 1, -196574, 65536, 1, -393215, 131072, 6, 65542, 262144, 2, 131078, 262144, 2, 196614, 262144, 2, 262150, 65536, 0, 327686, 65536, 1, 196612, 262144, 2, 262151, 65536, 0, 262152, 65536, 0, 6, 65536, 2, 29, 65536, 2, 196619, 262144, 2, 131093, 262144, 2, 65557, 262144, 2, -131072, 196608, 6, -196610, 65536, 6, -131074, 65536, 6, -65538, 65536, 6, -65537, 65536, 6, -131073, 65536, 6, -196609, 65536, 6, 1835007, 65536, 1, 1900578, 65536, 1, 33, 262144, 0, 2162686, 65536, 1, 2162687, 65536, 1, 2097152, 65536, 1, 2097153, 65536, 1, 2097154, 65536, 1, 2097155, 65536, 1, 2097156, 65536, 1, 2097157, 65536, 1, 2097158, 65536, 1, 2097159, 65536, 1, 2097160, 65536, 1, 2097161, 65536, 1, 2097162, 65536, 1, 2097163, 65536, 1, 2097164, 65536, 1, 2097165, 65536, 1, 2097166, 65536, 1, 2097167, 65536, 1, 2097168, 65536, 1, 2097169, 65536, 1, 2097170, 65536, 1, 2097171, 65536, 1, 2097172, 65536, 1, 2097173, 65536, 1, 2097174, 65536, 1, 2097175, 65536, 1, 2097176, 65536, 1, 2097177, 65536, 1, 2097178, 65536, 1, 2097179, 65536, 1, 2097180, 65536, 1, 2097181, 65536, 1, 2097182, 65536, 1, 2097183, 65536, 1, 2097184, 65536, 1, 2097185, 65536, 1, 2097186, 65536, 1, 2097187, 65536, 1, 2228222, 65536, 1, 2228223, 65536, 1, 2162688, 65536, 1, 2162689, 65536, 1, 2162690, 65536, 1, 2162691, 65536, 1, 2162692, 65536, 1, 2162693, 65536, 1, 2162694, 65536, 1, 2162695, 65536, 1, 2162696, 65536, 1, 2162697, 65536, 1, 2162698, 65536, 1, 2162699, 65536, 1, 2162700, 65536, 1, 2162701, 65536, 1, 2162702, 65536, 1, 2162703, 65536, 1, 2162704, 65536, 1, 2162705, 65536, 1, 2162706, 65536, 1, 2162707, 65536, 1, 2162708, 65536, 1, 2162709, 65536, 1, 2162710, 65536, 1, 2162711, 65536, 1, 2162712, 65536, 1, 2162713, 65536, 1, 2162714, 65536, 1, 2162715, 65536, 1, 2162716, 65536, 1, 2162717, 65536, 1, 2162718, 65536, 1, 2162719, 65536, 1, 2162720, 65536, 1, 2162721, 65536, 1, 2162722, 65536, 1, 2162723, 65536, 1, -393180, 65536, 1, -327644, 65536, 1, -262108, 65536, 1, -196572, 65536, 1, -131036, 65536, 1, -65500, 65536, 1, 36, 65536, 1, 65572, 65536, 1, 131108, 65536, 1, 196644, 65536, 1, 262180, 65536, 1, 327716, 65536, 1, 393252, 65536, 1, 458788, 65536, 1, 524324, 65536, 1, 589860, 65536, 1, 655396, 65536, 1, 720932, 65536, 1, 786468, 65536, 1, 852004, 65536, 1, 917540, 65536, 1, 983076, 65536, 1, 1048612, 65536, 1, 1114148, 65536, 1, 1179684, 65536, 1, 1245220, 65536, 1, 1310756, 65536, 1, 1376292, 65536, 1, 1441828, 65536, 1, 1507364, 65536, 1, 1572900, 65536, 1, 1638436, 65536, 1, 1703972, 65536, 1, 1769508, 65536, 1, 1835044, 65536, 1, 1900580, 65536, 1, 1966116, 65536, 1, 2031652, 65536, 1, 2097188, 65536, 1, 2162724, 65536, 1)
layer_1/name = "Ground Objects"
layer_1/tile_data = PackedInt32Array(1835039, 0, 4, 1441824, 0, 3, 1638424, 131072, 4, 1048591, 262144, 4, 1638408, 327680, 6, 1638409, 327680, 5, 1638410, 327680, 6, 1638411, 327680, 6, 1703947, 393216, 5, 1703946, 327680, 6, 1703945, 393216, 5, 1572873, 327680, 6, 1572874, 393216, 5, 1572876, 393216, 5, 1507340, 327680, 5, 1507339, 327680, 6, 1703948, 327680, 5, 1507337, 327680, 7, 1572875, 327680, 7, 1638412, 327680, 7, 1703949, 327680, 7, 1572892, 393216, 4, 1507346, 393216, 4, 589835, 393216, 1, 589836, 393216, 1, 524300, 393216, 1, 524301, 393216, 1, 458765, 393216, 1, 1310744, 327680, 1, 1310745, 327680, 1, 1245210, 393216, 0, 1376281, 393216, 0, 1900554, 0, 3, 1376275, 0, 3, 1507334, 131072, 4, 655374, 327680, 4, -65511, 393216, 4, 2097155, 327680, 7, 2097156, 327680, 5, 2162692, 327680, 5, 2162693, 393216, 6, 2097157, 327680, 7, 2097158, 393216, 6, 2162694, 327680, 7, 2162691, 327680, 7, 2097160, 327680, 7, 2097159, 327680, 7, 2097161, 327680, 7, 2097162, 393216, 6, 2097163, 393216, 6, 2097164, 393216, 5, 2097165, 327680, 7, 2097166, 327680, 7, 2097167, 393216, 6, 2097168, 393216, 5, 2097169, 327680, 7, 2097170, 327680, 7, 2097171, 327680, 7, 2097172, 393216, 5, 2097173, 393216, 5, 2097174, 393216, 5, 2097175, 393216, 5, 2097176, 393216, 5, 2097177, 327680, 7, 2097178, 327680, 6, 2162714, 393216, 5, 2162713, 327680, 7, 2162712, 393216, 6, 2162711, 393216, 6, 2162710, 393216, 6, 2162709, 393216, 6, 2162708, 393216, 6, 2162707, 393216, 6, 2162706, 393216, 5, 2162705, 327680, 7, 2162704, 327680, 5, 2162703, 327680, 7, 2162702, 327680, 7, 2162701, 327680, 5, 2162700, 327680, 7, 2162699, 327680, 7, 2162698, 327680, 7, 2162697, 327680, 7, 2162696, 393216, 5, 2162695, 327680, 7, 2097179, 327680, 7, 2097180, 327680, 7, 2097181, 327680, 7, 2097182, 327680, 7, 2162718, 327680, 7, 2162719, 327680, 7, 2162717, 393216, 6, 2162716, 393216, 5, 2162715, 327680, 7, 2097183, 327680, 5, 1179682, 196608, 3, 262179, 196608, 3, -262115, 196608, 3, -262124, 196608, 3, -196596, 196608, 3, -262132, 196608, 3, -262130, 196608, 3, -196595, 196608, 3, 196638, 0, 3, 196639, 0, 3, 131103, 0, 3, 786452, 0, 4, 1048607, 0, 4, 1114140, 327680, 1, 393239, 327680, 1, 655382, 327680, 1, 327700, 393216, 1, 458758, 393216, 1, 983046, 393216, 1, 1048583, 327680, 2, 983048, 327680, 2, 983047, 327680, 2, 1114121, 393216, 2, 1703936, 393216, 5, 1769471, 393216, 5, 1703935, 393216, 5, 1441790, 393216, 5, 1376255, 393216, 5, 1179647, 393216, 5, 1048575, 393216, 6, 983039, 327680, 5, 851967, 393216, 5, 851966, 393216, 5, 786430, 393216, 5, 720894, 327680, 6, 589823, 327680, 5, 458751, 393216, 5, 393215, 327680, 6, 262143, 327680, 5, 1638435, 393216, 6, 1310754, 327680, 6, 983075, 393216, 6, 852003, 327680, 6, 655395, 327680, 5, 655396, 393216, 5, 589860, 393216, 6, 393252, 393216, 5, 327716, 327680, 6, 262180, 327680, 6, 65570, 327680, 5, 1441827, 327680, 6, 1572898, 327680, 6, 1572884, 131072, 3, 1507352, 131072, 3, 1179671, 131072, 3, 2031639, 327680, 4, 2031651, 262144, 4, 131076, 0, 3, 131079, 0, 3, 1376265, 262144, 3, 1376271, 262144, 3, 786444, 262144, 3, 786439, 262144, 3, 1507343, 196608, 4)

[node name="Objective" parent="." instance=ExtResource("3_l27ho")]
position = Vector2(3930, 1511)

[node name="Camera2D" parent="." instance=ExtResource("4_ugg6l")]
position = Vector2(3168, 1440)
min_zoom = 0.5

[node name="Spawner" parent="." instance=ExtResource("5_jhdu7")]
visible = false

[node name="SpawnLocation1" parent="Spawner/SpawnContainer" index="0"]
position = Vector2(376, 392)

[node name="SpawnLocation2" parent="Spawner/SpawnContainer" index="1"]
position = Vector2(371, 2131)

[node name="SpawnLocation3" parent="Spawner/SpawnContainer" index="2"]
position = Vector2(364, 3706)

[node name="AircraftNavRegion" type="NavigationRegion2D" parent="."]
navigation_polygon = SubResource("NavigationPolygon_nxq2u")
navigation_layers = 167

[node name="TowerSlotPlacer" type="Node2D" parent="."]
script = ExtResource("9_tower_slot_placer")

[editable path="Spawner"]
